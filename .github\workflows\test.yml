on: [push, pull_request]
name: Test
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - name: Install Go
      uses: actions/setup-go@v5
      with:
        go-version: stable
    - name: Checkout code
      uses: actions/checkout@v4
    - name: Test
      run: |
        go test -v ./...
    - name: Test (embedded)
      run: |
        go test -v -tags embedded ./...
    - name: Test (portable)
      run: |
        go test -v -tags portable ./...
    - name: Benchmark
      run: |
        go test -timeout=1h -v -bench=. -run Bench ./...
