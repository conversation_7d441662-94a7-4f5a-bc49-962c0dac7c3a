version: "2"
linters:
  default: all
  settings:
    gosec:
      excludes:
        - G115
        - G306
        - G404
    goconst:
      min-occurrences: 4
    gocritic:
      disabled-checks:
        - singleCaseSwitch
    cyclop:
      max-complexity: 30
    lll:
      line-length: 150
    maintidx:
      under: 15
    nestif:
      min-complexity: 10
    revive:
      rules:
        - name: var-naming
          arguments:
            - ["ID"]
    stylecheck:
      initialisms:
        [
          "ACL",
          "API",
          "ASCII",
          "CPU",
          "CSS",
          "DNS",
          "EOF",
          "GUID",
          "HTML",
          "HTTP",
          "HTTPS",
          "IP",
          "JSON",
          "QPS",
          "RAM",
          "RPC",
          "SLA",
          "SMTP",
          "SQL",
          "SSH",
          "TCP",
          "TLS",
          "TTL",
          "UDP",
          "UI",
          "GID",
          "UID",
          "UUID",
          "URI",
          "URL",
          "UTF8",
          "VM",
          "XML",
          "XMPP",
          "XSRF",
          "XSS",
          "SIP",
          "R<PERSON>",
          "AMQP",
          "DB",
          "TS",
        ]
  issues:
    max-issues-per-linter: 0
    exclude-rules:
      - path: 'example_test\.go'
        text: "rewrite if-else to switch statement"
      - path: '(.+)_test\.go'
        text: "Use of weak random number generator"
      - path: '(.+)_test\.go'
        linters:
          - lll
  disable:
    - depguard
    - dupl
    - dupword
    - err113
    - errcheck
    - exhaustive
    - exhaustruct
    - funlen
    - gochecknoglobals
    - gochecknoinits
    - gocognit
    - gocritic
    - makezero
    - mnd
    - musttag
    - nlreturn
    - nolintlint
    - paralleltest
    - prealloc
    - recvcheck
    - staticcheck
    - testpackage
    - varnamelen
    - wastedassign
    - wsl
